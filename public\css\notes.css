/**
 * 笔记系统专用样式
 * 支持亮色/深色主题切换和移动端响应式设计
 */

/* =======================================
   主题变量定义
   ======================================= */
:root {
    /* 笔记系统专用颜色 */
    --notes-primary: #3B82F6;
    --notes-secondary: #6B7280;
    --notes-success: #10B981;
    --notes-warning: #F59E0B;
    --notes-error: #EF4444;
    --notes-info: #8B5CF6;
    
    /* 背景色 */
    --notes-bg-primary: #FFFFFF;
    --notes-bg-secondary: #F9FAFB;
    --notes-bg-tertiary: #F3F4F6;
    
    /* 文本颜色 */
    --notes-text-primary: #111827;
    --notes-text-secondary: #6B7280;
    --notes-text-tertiary: #9CA3AF;
    
    /* 边框颜色 */
    --notes-border-light: #E5E7EB;
    --notes-border-medium: #D1D5DB;
    --notes-border-dark: #9CA3AF;
    
    /* 阴影 */
    --notes-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --notes-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --notes-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* 圆角 */
    --notes-radius-sm: 0.375rem;
    --notes-radius-md: 0.5rem;
    --notes-radius-lg: 0.75rem;
    --notes-radius-xl: 1rem;
    
    /* 间距 */
    --notes-spacing-xs: 0.25rem;
    --notes-spacing-sm: 0.5rem;
    --notes-spacing-md: 1rem;
    --notes-spacing-lg: 1.5rem;
    --notes-spacing-xl: 2rem;
}

/* 深色主题变量 */
.dark {
    --notes-bg-primary: #1F2937;
    --notes-bg-secondary: #111827;
    --notes-bg-tertiary: #374151;

    --notes-text-primary: #F9FAFB;
    --notes-text-secondary: #D1D5DB;
    --notes-text-tertiary: #9CA3AF;

    --notes-border-light: #374151;
    --notes-border-medium: #4B5563;
    --notes-border-dark: #6B7280;

    --notes-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --notes-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --notes-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* 强制深色主题样式 */
.dark .sidebar-section {
    background: var(--notes-bg-primary) !important;
    border-color: var(--notes-border-light) !important;
}

.dark .sidebar-title {
    color: var(--notes-text-primary) !important;
}

.dark .category-item {
    color: var(--notes-text-secondary) !important;
}

.dark .category-item:hover {
    background: var(--notes-bg-tertiary) !important;
    color: var(--notes-text-primary) !important;
}

.dark .category-item.active {
    background: var(--notes-primary) !important;
    color: white !important;
}

.dark .category-count {
    background: var(--notes-bg-tertiary) !important;
    color: var(--notes-text-tertiary) !important;
}

.dark .category-item.active .category-count {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.dark .filter-btn {
    color: var(--notes-text-secondary) !important;
}

.dark .filter-btn:hover {
    background: var(--notes-bg-tertiary) !important;
    color: var(--notes-text-primary) !important;
}

.dark .filter-btn.active {
    background: var(--notes-primary) !important;
    color: white !important;
}

.dark .filter-count {
    background: var(--notes-bg-tertiary) !important;
    color: var(--notes-text-tertiary) !important;
}

.dark .filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* 深色主题下的主要内容区域 */
.dark .main-content {
    background: var(--notes-bg-secondary) !important;
    color: var(--notes-text-primary) !important;
}

.dark .content-header {
    background: var(--notes-bg-primary) !important;
    border-color: var(--notes-border-light) !important;
}

.dark .search-input {
    background: var(--notes-bg-tertiary) !important;
    border-color: var(--notes-border-medium) !important;
    color: var(--notes-text-primary) !important;
}

.dark .search-input::placeholder {
    color: var(--notes-text-tertiary) !important;
}

.dark .view-toggle-btn {
    color: var(--notes-text-secondary) !important;
}

.dark .view-toggle-btn:hover {
    background: var(--notes-bg-tertiary) !important;
    color: var(--notes-text-primary) !important;
}

.dark .view-toggle-btn.active {
    background: var(--notes-primary) !important;
    color: white !important;
}

.dark .sort-select {
    background: var(--notes-bg-tertiary) !important;
    border-color: var(--notes-border-medium) !important;
    color: var(--notes-text-primary) !important;
}

/* 深色主题下的空状态 */
.dark .empty-state {
    color: var(--notes-text-tertiary) !important;
}

.dark .empty-state h3 {
    color: var(--notes-text-secondary) !important;
}

/* 深色主题下的按钮 */
.dark .btn-primary {
    background: var(--notes-primary) !important;
    border-color: var(--notes-primary) !important;
    color: white !important;
}

.dark .btn-secondary {
    background: var(--notes-bg-tertiary) !important;
    border-color: var(--notes-border-medium) !important;
    color: var(--notes-text-primary) !important;
}

.dark .btn-secondary:hover {
    background: var(--notes-border-medium) !important;
}

/* =======================================
   笔记卡片样式
   ======================================= */
.note-card {
    background: var(--notes-bg-primary);
    border: 1px solid var(--notes-border-light);
    border-radius: var(--notes-radius-lg);
    box-shadow: var(--notes-shadow-sm);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.note-card:hover {
    box-shadow: var(--notes-shadow-md);
    transform: translateY(-1px);
    border-color: var(--notes-border-medium);
}

.note-card.pinned {
    border-left: 4px solid var(--notes-warning);
}

.note-card.pinned::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 20px 20px 0;
    border-color: transparent var(--notes-warning) transparent transparent;
}

.note-card.pinned::after {
    content: '\f08d';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 2px;
    right: 2px;
    color: white;
    font-size: 10px;
    z-index: 1;
}

/* 笔记标题 */
.note-title {
    color: var(--notes-text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.5;
    margin-bottom: var(--notes-spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 笔记内容预览 */
.note-content-preview {
    color: var(--notes-text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: var(--notes-spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 笔记元信息 */
.note-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--notes-text-tertiary);
    margin-bottom: var(--notes-spacing-sm);
}

.note-meta .category {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--notes-radius-sm);
    font-weight: 500;
}

.note-meta .stats {
    display: flex;
    align-items: center;
    gap: var(--notes-spacing-md);
}

/* 笔记标签 */
.note-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--notes-spacing-xs);
    margin-bottom: var(--notes-spacing-sm);
}

.note-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    border-radius: var(--notes-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.note-tag:hover {
    opacity: 0.8;
}

/* 笔记操作按钮 */
.note-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--notes-spacing-sm);
    padding-top: var(--notes-spacing-md);
    border-top: 1px solid var(--notes-border-light);
}

.note-action-btn {
    position: relative;
    padding: 0.75rem;
    border-radius: var(--notes-radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.75rem;
    min-height: 2.75rem;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.note-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.note-action-btn:hover::before {
    left: 100%;
}

.note-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.15), 0 4px 8px 0 rgba(0, 0, 0, 0.1);
}

.note-action-btn:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 主要按钮 - 查看 */
.note-action-btn.primary {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border: 1px solid #2563EB;
}

.note-action-btn.primary:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    border-color: #1D4ED8;
}

.note-action-btn.primary i {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 编辑按钮 */
.note-action-btn.edit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    border: 1px solid #059669;
}

.note-action-btn.edit:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
}

.note-action-btn.edit i {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 置顶按钮 */
.note-action-btn.pin {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
    border: 1px solid #D97706;
    position: relative;
}

.note-action-btn.pin.pinned {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    border-color: #DC2626;
}

.note-action-btn.pin.pinned::after {
    content: '';
    position: absolute;
    top: -3px;
    right: -3px;
    width: 12px;
    height: 12px;
    background: #FEF3C7;
    border-radius: 50%;
    box-shadow: 0 0 0 2px #F59E0B, 0 0 8px rgba(245, 158, 11, 0.4);
    animation: pulse 2s infinite;
}

.note-action-btn.pin.pinned {
    position: relative;
}

.note-action-btn.pin.pinned::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: inherit;
    background: linear-gradient(45deg, #F59E0B, #EF4444, #F59E0B);
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    }
}

/* 为所有按钮图标添加悬浮时的脉冲效果 */
.note-action-btn:hover i {
    animation: iconPulse 0.6s ease-in-out;
}

.note-action-btn.pin:hover {
    background: linear-gradient(135deg, #D97706 0%, #B45309 100%);
    border-color: #B45309;
}

.note-action-btn.pin.pinned:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    border-color: #B91C1C;
}

.note-action-btn.pin i {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 删除按钮 */
.note-action-btn.danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    border: 1px solid #DC2626;
}

.note-action-btn.danger:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    border-color: #B91C1C;
}

.note-action-btn.danger i {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 次要按钮 */
.note-action-btn.secondary {
    background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
    color: var(--notes-text-secondary);
    border: 1px solid var(--notes-border-medium);
}

.note-action-btn.secondary:hover {
    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
    border-color: var(--notes-border-dark);
    color: var(--notes-text-primary);
}

/* 深色主题下的按钮调整 */
.dark .note-action-btn.secondary {
    background: linear-gradient(135deg, #374151 0%, #4B5563 100%);
    color: var(--notes-text-secondary);
    border-color: var(--notes-border-medium);
}

.dark .note-action-btn.secondary:hover {
    background: linear-gradient(135deg, #4B5563 0%, #6B7280 100%);
    border-color: var(--notes-border-dark);
    color: var(--notes-text-primary);
}

/* =======================================
   侧边栏样式
   ======================================= */
.sidebar-section {
    background: var(--notes-bg-primary);
    border: 1px solid var(--notes-border-light);
    border-radius: var(--notes-radius-lg);
    box-shadow: var(--notes-shadow-sm);
    padding: var(--notes-spacing-lg);
    margin-bottom: var(--notes-spacing-lg);
}

.sidebar-title {
    color: var(--notes-text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--notes-spacing-md);
    display: flex;
    align-items: center;
    gap: var(--notes-spacing-sm);
}

/* 分类列表 */
.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--notes-spacing-sm) var(--notes-spacing-md);
    border-radius: var(--notes-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: var(--notes-spacing-xs);
}

.category-item:hover {
    background: var(--notes-bg-secondary);
}

.category-item.active {
    background: var(--notes-primary);
    color: white;
}

.category-item .category-info {
    display: flex;
    align-items: center;
    gap: var(--notes-spacing-sm);
}

.category-item .category-count {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: var(--notes-radius-sm);
    background: var(--notes-bg-tertiary);
    color: var(--notes-text-tertiary);
}

.category-item.active .category-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 标签云 */
.tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--notes-spacing-sm);
}

.tag-cloud-item {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--notes-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.tag-cloud-item:hover {
    transform: scale(1.05);
    box-shadow: var(--notes-shadow-sm);
}

.tag-cloud-item.active {
    box-shadow: var(--notes-shadow-md);
    transform: scale(1.1);
}

/* =======================================
   搜索和过滤样式
   ======================================= */
.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 1.5px solid var(--notes-border-light);
    border-radius: var(--notes-radius-lg);
    background: var(--notes-bg-primary);
    color: var(--notes-text-primary);
    font-size: 0.875rem;
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    outline: none;
    border-color: var(--notes-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.search-input:hover:not(:focus) {
    border-color: var(--notes-border-medium);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
}

.search-input::placeholder {
    color: var(--notes-text-tertiary);
    opacity: 0.8;
}

.search-icon {
    position: absolute;
    left: var(--notes-spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--notes-text-tertiary);
}

/* 过滤器按钮 */
.filter-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--notes-spacing-sm) var(--notes-spacing-md);
    border: none;
    background: transparent;
    color: var(--notes-text-secondary);
    font-size: 0.875rem;
    border-radius: var(--notes-radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.filter-btn:hover {
    background: var(--notes-bg-secondary);
    color: var(--notes-text-primary);
}

.filter-btn.active {
    background: var(--notes-primary);
    color: white;
}

.filter-count {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: var(--notes-radius-sm);
    background: var(--notes-bg-tertiary);
    color: var(--notes-text-tertiary);
}

.filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* =======================================
   模态框样式
   ======================================= */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--notes-spacing-md);
}

.modal-content {
    background: var(--notes-bg-primary);
    border-radius: var(--notes-radius-xl);
    box-shadow: var(--notes-shadow-lg);
    width: 100%;
    max-width: 4rem;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--notes-spacing-md);
    border-bottom: 1px solid var(--notes-border-light);
}

@media (min-width: 640px) {
    .modal-header {
        padding: var(--notes-spacing-xl);
    }
}

.modal-title {
    color: var(--notes-text-primary);
    font-size: 1.125rem;
    font-weight: 600;
}

@media (min-width: 640px) {
    .modal-title {
        font-size: 1.25rem;
    }
}

.modal-close {
    color: var(--notes-text-tertiary);
    font-size: 1.25rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--notes-text-secondary);
}

.modal-body {
    padding: var(--notes-spacing-md);
    overflow-y: auto;
    flex: 1;
}

@media (min-width: 640px) {
    .modal-body {
        padding: var(--notes-spacing-xl);
    }
}

/* =======================================
   表单样式 - 优化间距和视觉呼吸感
   ======================================= */
.form-group {
    margin-bottom: var(--notes-spacing-lg);
}

.form-label {
    display: block;
    color: var(--notes-text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.625rem;
    letter-spacing: 0.025em;
}

/* 基础表单元素 - 增强内边距和视觉效果 */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1.5px solid var(--notes-border-light);
    border-radius: var(--notes-radius-md);
    background: var(--notes-bg-primary);
    color: var(--notes-text-primary);
    font-size: 0.875rem;
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 聚焦状态 - 增强视觉反馈 */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--notes-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* 悬浮状态 - 微妙的交互反馈 */
.form-input:hover:not(:focus),
.form-select:hover:not(:focus),
.form-textarea:hover:not(:focus) {
    border-color: var(--notes-border-medium);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
}

/* 文本域特殊样式 */
.form-textarea {
    resize: vertical;
    min-height: 140px;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.6;
}

/* 选择框特殊样式 */
.form-select {
    padding-right: 2.5rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25rem 1.25rem;
    appearance: none;
}

/* =======================================
   深色主题下的表单元素优化
   ======================================= */
.dark .form-input,
.dark .form-select,
.dark .form-textarea {
    background: var(--notes-bg-secondary);
    border-color: var(--notes-border-medium);
    color: var(--notes-text-primary);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .form-input:focus,
.dark .form-select:focus,
.dark .form-textarea:focus {
    border-color: var(--notes-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.3);
}

.dark .form-input:hover:not(:focus),
.dark .form-select:hover:not(:focus),
.dark .form-textarea:hover:not(:focus) {
    border-color: var(--notes-border-dark);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
}

.dark .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.dark .search-input {
    background: var(--notes-bg-secondary);
    border-color: var(--notes-border-medium);
    color: var(--notes-text-primary);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .search-input:focus {
    border-color: var(--notes-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.3);
}

.dark .search-input:hover:not(:focus) {
    border-color: var(--notes-border-dark);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
}

.dark .search-input::placeholder {
    color: var(--notes-text-tertiary);
    opacity: 0.7;
}

/* =======================================
   表单元素状态增强
   ======================================= */
/* 禁用状态 */
.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
    background-color: var(--notes-bg-tertiary);
    border-color: var(--notes-border-light);
    color: var(--notes-text-tertiary);
    cursor: not-allowed;
    opacity: 0.6;
}

.dark .form-input:disabled,
.dark .form-select:disabled,
.dark .form-textarea:disabled {
    background-color: var(--notes-bg-tertiary);
    border-color: var(--notes-border-medium);
    color: var(--notes-text-tertiary);
}

/* 错误状态 */
.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.dark .form-input.error,
.dark .form-select.error,
.dark .form-textarea.error {
    border-color: #f87171;
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2);
}

/* 成功状态 */
.form-input.success,
.form-select.success,
.form-textarea.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.dark .form-input.success,
.dark .form-select.success,
.dark .form-textarea.success {
    border-color: #34d399;
    box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.2);
}

/* 表单帮助文本 */
.form-help {
    margin-top: 0.375rem;
    font-size: 0.75rem;
    color: var(--notes-text-tertiary);
    line-height: 1.4;
}

.form-help.error {
    color: #ef4444;
}

.form-help.success {
    color: #10b981;
}

.dark .form-help.error {
    color: #f87171;
}

.dark .form-help.success {
    color: #34d399;
}

/* =======================================
   通用按钮样式
   ======================================= */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--notes-spacing-sm);
    padding: var(--notes-spacing-md) var(--notes-spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--notes-radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border-color: #2563EB;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    border-color: #1D4ED8;
}

.btn-secondary {
    background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
    color: var(--notes-text-secondary);
    border-color: var(--notes-border-medium);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
    border-color: var(--notes-border-dark);
    color: var(--notes-text-primary);
}

.btn-success {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    border-color: #059669;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
}

.btn-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
    border-color: #D97706;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #D97706 0%, #B45309 100%);
    border-color: #B45309;
}

.btn-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    border-color: #DC2626;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    border-color: #B91C1C;
}

.btn-sm {
    padding: var(--notes-spacing-sm) var(--notes-spacing-md);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--notes-spacing-lg) var(--notes-spacing-xl);
    font-size: 1rem;
}

/* 深色主题下的通用按钮调整 */
.dark .btn-secondary {
    background: linear-gradient(135deg, #374151 0%, #4B5563 100%);
    color: var(--notes-text-secondary);
    border-color: var(--notes-border-medium);
}

.dark .btn-secondary:hover {
    background: linear-gradient(135deg, #4B5563 0%, #6B7280 100%);
    border-color: var(--notes-border-dark);
    color: var(--notes-text-primary);
}

/* =======================================
   导航栏按钮样式
   ======================================= */
.nav-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: 1px solid transparent;
    border-radius: var(--notes-radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    overflow: hidden;
    min-width: 2.75rem;
    min-height: 2.75rem;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.nav-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 主题切换按钮 */
.theme-toggle-btn {
    background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
    color: white;
    border-color: #4B5563;
}

.theme-toggle-btn:hover {
    background: linear-gradient(135deg, #4B5563 0%, #374151 100%);
    border-color: #374151;
}

.dark .theme-toggle-btn {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
    border-color: #D97706;
}

.dark .theme-toggle-btn:hover {
    background: linear-gradient(135deg, #D97706 0%, #B45309 100%);
    border-color: #B45309;
}

/* 新建笔记按钮 */
.new-note-btn {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    border-color: #059669;
    position: relative;
}

.new-note-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
}

.new-note-btn::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #34D399;
    border-radius: 50%;
    box-shadow: 0 0 0 2px white;
    animation: pulse 2s infinite;
}

/* 返回首页按钮 */
.home-btn {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    color: white;
    border-color: #7C3AED;
}

.home-btn:hover {
    background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
    border-color: #6D28D9;
}

/* =======================================
   视图切换按钮样式
   ======================================= */
.view-toggle-group {
    display: flex;
    border: 1px solid var(--notes-border-medium);
    border-radius: var(--notes-radius-md);
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.view-toggle-btn {
    position: relative;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--notes-bg-primary);
    color: var(--notes-text-secondary);
    overflow: hidden;
}

.view-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.view-toggle-btn:hover::before {
    left: 100%;
}

.view-toggle-btn:hover {
    background: var(--notes-bg-secondary);
    color: var(--notes-text-primary);
}

.view-toggle-btn.active {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    box-shadow: inset 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.view-toggle-btn.active:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
}

/* 深色主题下的视图切换按钮 */
.dark .view-toggle-group {
    border-color: var(--notes-border-medium);
}

.dark .view-toggle-btn {
    background: var(--notes-bg-primary);
    color: var(--notes-text-secondary);
}

.dark .view-toggle-btn:hover {
    background: var(--notes-bg-tertiary);
    color: var(--notes-text-primary);
}

/* =======================================
   分页按钮样式
   ======================================= */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--notes-border-medium);
    border-radius: var(--notes-radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--notes-bg-primary);
    color: var(--notes-text-secondary);
    text-decoration: none;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.pagination-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.pagination-btn:hover::before {
    left: 100%;
}

.pagination-btn:hover {
    transform: translateY(-1px);
    background: var(--notes-bg-secondary);
    color: var(--notes-text-primary);
    border-color: var(--notes-border-dark);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.pagination-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.pagination-btn.active {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border-color: #2563EB;
    box-shadow: 0 4px 12px 0 rgba(59, 130, 246, 0.3), 0 2px 4px 0 rgba(59, 130, 246, 0.2);
}

.pagination-btn.active:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    border-color: #1D4ED8;
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    color: var(--notes-text-tertiary);
    font-weight: 500;
}

/* 深色主题下的分页按钮 */
.dark .pagination-btn {
    background: var(--notes-bg-primary);
    color: var(--notes-text-secondary);
    border-color: var(--notes-border-medium);
}

.dark .pagination-btn:hover {
    background: var(--notes-bg-tertiary);
    color: var(--notes-text-primary);
    border-color: var(--notes-border-dark);
}

/* =======================================
   管理模态框美化样式
   ======================================= */

/* 管理项目容器 */
.manager-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.dark .manager-item {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
}

/* 悬浮效果 */
.manager-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.dark .manager-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: #60a5fa;
}

/* 光泽效果 */
.manager-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.manager-item:hover::before {
    left: 100%;
}

/* 项目内容区域 */
.manager-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

/* 图标样式 */
.manager-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 10px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.dark .manager-item-icon {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

/* 信息区域 */
.manager-item-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* 名称样式 */
.manager-item-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
}

.dark .manager-item-name {
    color: #f9fafb;
}

/* 数量样式 */
.manager-item-count {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.dark .manager-item-count {
    color: #9ca3af;
}

/* 删除按钮 */
.manager-item-delete {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.manager-item:hover .manager-item-delete {
    opacity: 1;
    transform: scale(1);
}

.manager-item-delete:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.manager-item-delete:active {
    transform: scale(0.95);
}

/* 空状态样式 */
.manager-empty {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.dark .manager-empty {
    color: #9ca3af;
}

/* =======================================
   确认对话框样式修复
   ======================================= */
/* 确认对话框标题 */
.confirm-dialog-title {
    color: #111827 !important;
    font-weight: 600 !important;
}

.dark .confirm-dialog-title {
    color: #ffffff !important;
}

/* 确认对话框消息 */
.confirm-dialog-message {
    color: #374151 !important;
    line-height: 1.6 !important;
}

.dark .confirm-dialog-message {
    color: #d1d5db !important;
}

/* 确认对话框取消按钮 */
.confirm-dialog-cancel-btn {
    color: #374151 !important;
    font-weight: 500 !important;
}

.dark .confirm-dialog-cancel-btn {
    color: #f3f4f6 !important;
}

/* 模态框关闭按钮 */
.modal-close-btn {
    color: #374151 !important;
    font-weight: 500 !important;
}

.dark .modal-close-btn {
    color: #f3f4f6 !important;
}

/* =======================================
   详情模态框滚动优化
   ======================================= */
/* 详情模态框内容区域 */
#noteDetailModal .overflow-y-auto {
    max-height: calc(90vh - 140px) !important;
    overflow-y: auto !important;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.dark #noteDetailModal .overflow-y-auto {
    scrollbar-color: #64748b #374151;
}

/* WebKit浏览器滚动条样式 */
#noteDetailModal .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#noteDetailModal .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.dark #noteDetailModal .overflow-y-auto::-webkit-scrollbar-track {
    background: #374151;
}

#noteDetailModal .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.dark #noteDetailModal .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #64748b;
}

#noteDetailModal .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark #noteDetailModal .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #475569;
}

/* 详情内容区域 */
#detailContent {
    word-wrap: break-word;
    word-break: break-word;
    line-height: 1.6;
}

/* 确保长文本不会撑破容器 */
#detailContent .prose {
    max-width: none !important;
    word-wrap: break-word;
}

#detailContent .prose p {
    margin-bottom: 1rem;
}

#detailContent .prose pre,
#detailContent .prose code {
    white-space: pre-wrap;
    word-break: break-all;
}

/* =======================================
   图片预览样式
   ======================================= */
/* 图片容器网格布局 */
#detailImages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* 图片悬浮效果 */
#detailImages .relative {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#detailImages .relative:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.dark #detailImages .relative {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark #detailImages .relative:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* 图片预览图标 */
#detailImages .fa-search-plus {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 移动端适配 */
@media (max-width: 640px) {
    #detailImages {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    #detailImages img {
        height: 8rem !important;
    }
}

/* =======================================
   优雅笔记卡片设计 - 全新美观版本
   ======================================= */
.elegant-note-card {
    background: linear-gradient(145deg,
        var(--notes-bg-primary) 0%,
        rgba(255, 255, 255, 0.8) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.elegant-note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.8) 50%,
        transparent 100%);
    z-index: 1;
}

.elegant-note-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.4);
}

.elegant-note-card.is-pinned {
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow:
        0 8px 32px rgba(245, 158, 11, 0.2),
        0 2px 8px rgba(245, 158, 11, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.elegant-note-card.is-pinned:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(245, 158, 11, 0.3),
        0 8px 24px rgba(245, 158, 11, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* 置顶徽章 */
.pin-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    box-shadow:
        0 4px 12px rgba(245, 158, 11, 0.4),
        0 2px 4px rgba(245, 158, 11, 0.3);
    z-index: 2;
    animation: pinPulse 2s ease-in-out infinite;
}

@keyframes pinPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 卡片头部 */
.card-header {
    padding: 24px 24px 16px 24px;
    position: relative;
}

.title-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
}

.note-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--notes-text-primary);
    line-height: 1.3;
    margin: 0;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    letter-spacing: -0.025em;
    padding-right: 12px;
}

.elegant-note-card.is-pinned .note-title {
    color: #92400e;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.meta-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-count {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: var(--notes-text-tertiary);
    background: rgba(0, 0, 0, 0.05);
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.elegant-note-card.is-pinned .view-count {
    background: rgba(146, 64, 14, 0.15);
    color: #92400e;
}

.category-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.elegant-category {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--category-color, var(--notes-text-secondary));
    background: rgba(var(--category-color), 0.1);
    padding: 6px 12px;
    border-radius: 16px;
    border: 1px solid rgba(var(--category-color), 0.2);
}

.elegant-category.placeholder {
    color: var(--notes-text-tertiary);
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
}

.elegant-note-card.is-pinned .elegant-category {
    background: rgba(146, 64, 14, 0.15);
    border-color: rgba(146, 64, 14, 0.3);
    color: #92400e;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: var(--notes-text-tertiary);
    font-weight: 500;
}

.elegant-note-card.is-pinned .author-info {
    color: #92400e;
}

/* 卡片主体 */
.card-content {
    padding: 0 24px 16px 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.content-preview {
    color: var(--notes-text-secondary);
    font-size: 0.9rem;
    line-height: 1.7;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.elegant-note-card.is-pinned .content-preview {
    color: #92400e;
}

/* 笔记图片预览 */
.note-images-preview {
    display: flex;
    gap: 8px;
    margin: 12px 0;
    flex-wrap: wrap;
}

.note-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid var(--notes-border-light);
    transition: all 0.2s ease;
}

.note-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .note-thumbnail {
    border-color: var(--notes-border-medium);
}

.more-images {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    border: 1px solid var(--notes-border-light);
    background: var(--notes-bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--notes-text-tertiary);
    font-weight: 500;
}

.dark .more-images {
    border-color: var(--notes-border-medium);
    background: var(--notes-bg-tertiary);
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: auto;
}

.elegant-tag {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    background: linear-gradient(135deg,
        rgba(var(--tag-color), 0.1) 0%,
        rgba(var(--tag-color), 0.05) 100%);
    color: var(--tag-color);
    border: 1px solid rgba(var(--tag-color), 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.elegant-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%);
    transition: left 0.5s ease;
}

.elegant-tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--tag-color), 0.3);
    border-color: rgba(var(--tag-color), 0.4);
}

.elegant-tag:hover::before {
    left: 100%;
}

.elegant-note-card.is-pinned .elegant-tag {
    background: rgba(146, 64, 14, 0.15);
    color: #92400e;
    border-color: rgba(146, 64, 14, 0.3);
}

/* 卡片底部 */
.card-footer {
    padding: 12px 24px 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-top: auto;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(0, 0, 0, 0.02) 100%);
    backdrop-filter: blur(5px);
}

.elegant-note-card.is-pinned .card-footer {
    border-top-color: rgba(146, 64, 14, 0.2);
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(146, 64, 14, 0.05) 100%);
}

.time-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 8px 0;
}

.created-time,
.updated-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.7rem;
    color: var(--notes-text-tertiary);
    font-weight: 500;
    background: rgba(0, 0, 0, 0.03);
    padding: 4px 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.created-time:hover,
.updated-time:hover {
    background: rgba(0, 0, 0, 0.06);
    transform: scale(1.02);
}

.elegant-note-card.is-pinned .created-time,
.elegant-note-card.is-pinned .updated-time {
    color: #92400e;
    background: rgba(146, 64, 14, 0.1);
}

.elegant-note-card.is-pinned .created-time:hover,
.elegant-note-card.is-pinned .updated-time:hover {
    background: rgba(146, 64, 14, 0.15);
}

.created-time i,
.updated-time i {
    font-size: 0.65rem;
    opacity: 0.8;
}

.action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

/* 优雅按钮样式 */
.elegant-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.elegant-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
}

.elegant-action-btn:hover::before {
    width: 100%;
    height: 100%;
}

.elegant-action-btn:hover {
    transform: translateY(-3px) scale(1.1);
}

.elegant-action-btn:active {
    transform: translateY(-1px) scale(0.95);
}

.elegant-action-btn.primary {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border: 1px solid #2563EB;
    box-shadow:
        0 4px 15px rgba(59, 130, 246, 0.4),
        0 2px 8px rgba(59, 130, 246, 0.2);
}

.elegant-action-btn.primary:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    border-color: #1D4ED8;
    box-shadow:
        0 8px 25px rgba(37, 99, 235, 0.5),
        0 4px 12px rgba(37, 99, 235, 0.3);
}

.elegant-action-btn.edit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    border: 1px solid #059669;
    box-shadow:
        0 4px 15px rgba(16, 185, 129, 0.4),
        0 2px 8px rgba(16, 185, 129, 0.2);
}

.elegant-action-btn.edit:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
    box-shadow:
        0 8px 25px rgba(5, 150, 105, 0.5),
        0 4px 12px rgba(5, 150, 105, 0.3);
}

.elegant-action-btn.pin {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
    border: 1px solid #D97706;
    box-shadow:
        0 4px 15px rgba(245, 158, 11, 0.4),
        0 2px 8px rgba(245, 158, 11, 0.2);
}

.elegant-action-btn.pin:hover {
    background: linear-gradient(135deg, #D97706 0%, #B45309 100%);
    border-color: #B45309;
    box-shadow:
        0 8px 25px rgba(217, 119, 6, 0.5),
        0 4px 12px rgba(217, 119, 6, 0.3);
}

.elegant-action-btn.pin.active {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    border: 1px solid #DC2626;
    box-shadow:
        0 4px 15px rgba(239, 68, 68, 0.4),
        0 2px 8px rgba(239, 68, 68, 0.2);
}

.elegant-action-btn.pin.active:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    border-color: #B91C1C;
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.5),
        0 4px 12px rgba(220, 38, 38, 0.3);
}

.elegant-action-btn.delete {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    border: 1px solid #DC2626;
    box-shadow:
        0 4px 15px rgba(239, 68, 68, 0.4),
        0 2px 8px rgba(239, 68, 68, 0.2);
}

.elegant-action-btn.delete:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    border-color: #B91C1C;
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.5),
        0 4px 12px rgba(220, 38, 38, 0.3);
}

/* =======================================
   深色主题适配
   ======================================= */
.dark .elegant-note-card {
    background: linear-gradient(145deg,
        #1f2937 0%,
        #111827 100%);
    border-color: rgba(75, 85, 99, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .elegant-note-card:hover {
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 24px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(75, 85, 99, 0.5);
}

.dark .elegant-note-card.is-pinned {
    border-color: rgba(245, 158, 11, 0.4);
}

.dark .elegant-note-card.is-pinned .note-title,
.dark .elegant-note-card.is-pinned .view-count,
.dark .elegant-note-card.is-pinned .elegant-category,
.dark .elegant-note-card.is-pinned .author-info,
.dark .elegant-note-card.is-pinned .content-preview,
.dark .elegant-note-card.is-pinned .created-time,
.dark .elegant-note-card.is-pinned .updated-time {
    color: #fbbf24;
}

.dark .elegant-note-card.is-pinned .elegant-tag {
    background: rgba(251, 191, 36, 0.15);
    color: #fbbf24;
    border-color: rgba(251, 191, 36, 0.3);
}

.dark .card-footer {
    border-top-color: rgba(75, 85, 99, 0.3);
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(255, 255, 255, 0.02) 100%);
}

/* 深色主题下的功能按钮 */
.dark .elegant-action-btn.primary {
    background: var(--notes-primary) !important;
    border-color: var(--notes-primary) !important;
    color: white !important;
}

.dark .elegant-action-btn.primary:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%) !important;
    border-color: #1D4ED8 !important;
}

.dark .elegant-action-btn.edit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border-color: #059669 !important;
    color: white !important;
}

.dark .elegant-action-btn.edit:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    border-color: #047857 !important;
}

.dark .elegant-action-btn.pin {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
    border-color: #D97706 !important;
    color: white !important;
}

.dark .elegant-action-btn.pin:hover {
    background: linear-gradient(135deg, #D97706 0%, #B45309 100%) !important;
    border-color: #B45309 !important;
}

.dark .elegant-action-btn.pin.active {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
    border-color: #DC2626 !important;
    color: white !important;
}

.dark .elegant-action-btn.pin.active:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
    border-color: #B91C1C !important;
}

.dark .elegant-action-btn.delete {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
    border-color: #DC2626 !important;
    color: white !important;
}

.dark .elegant-action-btn.delete:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
    border-color: #B91C1C !important;
}

/* =======================================
   图片上传区域样式
   ======================================= */
.image-upload-area {
    border: 2px dashed var(--notes-border-light);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    background: var(--notes-bg-secondary);
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: var(--notes-primary);
    background: rgba(59, 130, 246, 0.05);
}

.dark .image-upload-area {
    border-color: var(--notes-border-medium);
    background: var(--notes-bg-tertiary);
}

.dark .image-upload-area:hover {
    border-color: var(--notes-primary);
    background: rgba(59, 130, 246, 0.1);
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.upload-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.upload-hint {
    margin-top: 12px;
    font-size: 0.75rem;
    color: var(--notes-text-tertiary);
}

/* 图片预览区域 */
#imagePreview {
    margin-top: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
}

.image-preview-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: var(--notes-bg-secondary);
    border: 1px solid var(--notes-border-light);
    transition: all 0.2s ease;
}

.image-preview-item:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .image-preview-item {
    background: var(--notes-bg-tertiary);
    border-color: var(--notes-border-medium);
}

.image-preview-item img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

.image-preview-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
}

.image-preview-item:hover .image-preview-remove {
    opacity: 1;
}

.image-preview-remove:hover {
    background: rgba(220, 38, 38, 1);
    transform: scale(1.1);
}

.image-preview-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    font-size: 10px;
    padding: 8px 4px 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* =======================================
   响应式设计
   ======================================= */
@media (max-width: 768px) {
    .elegant-note-card {
        border-radius: 16px;
    }

    .pin-badge {
        top: 12px;
        right: 12px;
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .card-header {
        padding: 20px 20px 12px 20px;
    }

    .note-title {
        font-size: 1.1rem;
        padding-right: 8px;
    }

    .view-count {
        font-size: 0.75rem;
        padding: 3px 6px;
    }

    .category-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .elegant-category {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .card-content {
        padding: 0 20px 12px 20px;
        gap: 12px;
    }

    .content-preview {
        font-size: 0.85rem;
        -webkit-line-clamp: 2;
    }

    .elegant-tag {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .card-footer {
        padding: 10px 20px 16px 20px;
        gap: 12px;
    }

    .time-info {
        gap: 16px;
        flex-wrap: wrap;
        padding: 6px 0;
    }

    .created-time,
    .updated-time {
        font-size: 0.65rem;
        padding: 3px 6px;
    }

    .action-buttons {
        gap: 16px;
    }

    .elegant-action-btn {
        width: 44px;
        height: 44px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .elegant-note-card {
        border-radius: 12px;
    }

    .card-header {
        padding: 16px 16px 8px 16px;
    }

    .card-content {
        padding: 0 16px 8px 16px;
    }

    .card-footer {
        padding: 8px 16px 12px 16px;
        gap: 10px;
    }

    .time-info {
        gap: 12px;
        padding: 4px 0;
    }

    .note-title {
        font-size: 1rem;
    }

    .content-preview {
        font-size: 0.8rem;
    }
}

/* =======================================
   响应式设计
   ======================================= */
@media (max-width: 768px) {
    .note-card {
        margin-bottom: var(--notes-spacing-md);
    }
    
    .note-title {
        font-size: 1rem;
    }
    
    .note-content-preview {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
    }
    
    .note-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--notes-spacing-sm);
    }
    
    .note-actions {
        flex-wrap: wrap;
        gap: var(--notes-spacing-xs);
        justify-content: center;
    }

    .note-action-btn {
        font-size: 0.875rem;
        padding: 0.625rem;
        min-width: 2.5rem;
        min-height: 2.5rem;
        flex: 0 0 auto;
    }

    /* 移动端按钮触摸优化 */
    .note-action-btn:active {
        animation: buttonPress 0.1s ease;
    }
    
    .sidebar-section {
        padding: var(--notes-spacing-md);
        margin-bottom: var(--notes-spacing-md);
    }
    
    /* 调整模态框最大宽度和内边距 */
    #noteModal .bg-white,
    #noteDetailModal .bg-white,
    .dark #noteModal .bg-gray-800,
    .dark #noteDetailModal .bg-gray-800 {
        width: 95% !important;
        max-height: 85vh !important;
    }
    
    /* 调整表单元素大小 */
    #noteTitle, #noteCategory, #noteContent {
        font-size: 14px;
    }
    
    /* 调整内容容器的最大高度 */
    #noteModal .overflow-y-auto,
    #noteDetailModal .overflow-y-auto {
        max-height: calc(85vh - 100px) !important;
    }
    
    /* 确保表单间距适合移动设备 */
    #noteForm .space-y-4 > div {
        margin-bottom: 0.75rem;
    }
    
    .btn {
        padding: var(--notes-spacing-sm) var(--notes-spacing-md);
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .note-tags {
        gap: 0.125rem;
    }
    
    .note-tag {
        font-size: 0.7rem;
        padding: 0.1rem 0.375rem;
    }
    
    .tags-cloud {
        gap: 0.25rem;
    }
    
    .tag-cloud-item {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    
    .category-item {
        padding: var(--notes-spacing-xs) var(--notes-spacing-sm);
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        padding: 0.75rem 0.875rem;
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .form-textarea {
        padding: 0.875rem;
        min-height: 120px;
    }

    .search-input {
        padding: 0.75rem 0.875rem 0.75rem 2.75rem;
        font-size: 0.8rem;
    }
    
    /* 进一步优化超小屏幕的模态框 */
    #noteModal, #noteDetailModal {
        padding: 0.5rem;
    }
    
    #noteModal .p-4,
    #noteDetailModal .p-4 {
        padding: 0.75rem;
    }
    
    /* 文本区域高度调整 */
    #noteContent {
        min-height: 120px;
        max-height: 180px;
    }
    
    /* 优化按钮和标签的大小 */
    #noteForm button,
    #detailContent {
        font-size: 0.75rem;
    }
    
    /* 确保时间戳在移动设备上不会溢出 */
    #detailCreatedAt, 
    #detailUpdatedAt {
        word-break: break-all;
    }
}

/* =======================================
   动画效果
   ======================================= */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-in {
    animation: slideIn 0.3s ease;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--notes-border-light);
    border-radius: 50%;
    border-top-color: var(--notes-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes buttonPress {
    0% {
        transform: translateY(-1px) scale(1);
    }
    50% {
        transform: translateY(0) scale(0.98);
    }
    100% {
        transform: translateY(-1px) scale(1);
    }
}

/* =======================================
   工具提示
   ======================================= */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--notes-text-primary);
    color: var(--notes-bg-primary);
    padding: var(--notes-spacing-sm) var(--notes-spacing-md);
    border-radius: var(--notes-radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 10;
}

.tooltip:hover::before {
    opacity: 1;
}

/* =======================================
   滚动条样式
   ======================================= */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--notes-bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--notes-border-medium);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--notes-border-dark);
}
