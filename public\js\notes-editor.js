/**
 * 笔记编辑器模块
 * 负责笔记的创建和编辑功能
 */

class NotesEditor {
    constructor() {
        this.isEditing = false;
        this.currentNoteId = null;
        this.categories = [];
        this.tags = [];
        this.selectedTags = [];
        this.originalData = null; // 存储原始数据用于比较
        this.justSaved = false; // 添加标志，标记是否刚刚保存成功
        this.existingImages = []; // 存储现有图片
        this.imagesToDelete = []; // 存储要删除的图片
        this.eventsBound = false; // 事件绑定标志

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadFormData();
    }
    
    bindEvents() {
        // 防止重复绑定事件
        if (this.eventsBound) {
            return;
        }
        this.eventsBound = true;

        // 模态框关闭事件 - 添加强制关闭参数
        document.getElementById('closeModalBtn').addEventListener('click', () => {
            this.closeModal(true); // 强制关闭，不检查未保存的更改
        });

        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.closeModal(true); // 强制关闭，不检查未保存的更改
        });

        // 点击模态框背景关闭
        document.getElementById('noteModal').addEventListener('click', (e) => {
            if (e.target.id === 'noteModal') {
                this.closeModal(); // 保留未保存检查
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('noteModal').classList.contains('hidden')) {
                this.closeModal(); // 保留未保存检查
            }
        });

        // 表单提交
        document.getElementById('noteForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveNote();
        });

        // 标签容器点击事件（用于显示标签选择器）
        document.getElementById('tagsContainer').addEventListener('click', () => {
            this.showTagSelector();
        });
    }
    
    async loadFormData() {
        try {
            // 加载分类数据
            await this.loadCategories();
            
            // 加载标签数据
            await this.loadTags();
            
        } catch (error) {
            console.error('加载表单数据失败:', error);
        }
    }
    
    async loadCategories() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/categories', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('加载分类失败');
            }
            
            const data = await response.json();
            this.categories = data.data;
            console.log('加载的分类数据:', this.categories);
            this.renderCategoryOptions();
            
        } catch (error) {
            console.error('加载分类失败:', error);
        }
    }
    
    renderCategoryOptions() {
        const select = document.getElementById('noteCategory');
        
        let html = '<option value="">选择分类</option>';
        this.categories.forEach(category => {
            html += `<option value="${category.id}">${category.name}</option>`;
        });
        
        select.innerHTML = html;
    }
    
    async loadTags() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/tags', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('加载标签失败');
            }
            
            const data = await response.json();
            this.tags = data.data;
            console.log('加载的标签数据:', this.tags);
            
        } catch (error) {
            console.error('加载标签失败:', error);
        }
    }
    
    openModal(noteData = null) {
        console.log('打开模态框 - 输入数据:', noteData);
        console.log('打开前状态:', { isEditing: this.isEditing, currentNoteId: this.currentNoteId });

        // 仅当不提供noteData时，才重置编辑状态 (避免覆盖editNote中设置的状态)
        if (noteData) {
            this.isEditing = true;
            this.currentNoteId = noteData.id;
        } else {
            this.isEditing = false;
            this.currentNoteId = null;
        }
        
        // 设置模态框标题
        document.getElementById('modalTitle').textContent = this.isEditing ? '编辑笔记' : '新建笔记';
        
        // 重置表单
        this.resetForm();
        
        // 如果是编辑模式，填充数据
        if (noteData) {
            this.fillForm(noteData);
        } else {
            // 处理新建笔记时的置顶选项
            const isPinnedCheckbox = document.getElementById('notePinned');
            if (isPinnedCheckbox) {
                // 普通用户不能置顶，将选项禁用
                if (window.notesManager && window.notesManager.userRole !== 'admin') {
                    isPinnedCheckbox.disabled = true;
                    isPinnedCheckbox.parentElement.classList.add('opacity-50');
                    isPinnedCheckbox.parentElement.title = '只有管理员可以置顶笔记';
                } else {
                    isPinnedCheckbox.disabled = false;
                    isPinnedCheckbox.parentElement.classList.remove('opacity-50');
                    isPinnedCheckbox.parentElement.title = '';
                }
            }
        }
        
        // 显示模态框
        document.getElementById('noteModal').classList.remove('hidden');
        
        // 聚焦到标题输入框
        setTimeout(() => {
            document.getElementById('noteTitle').focus();
        }, 100);

        console.log('打开后状态:', { isEditing: this.isEditing, currentNoteId: this.currentNoteId });
    }
    
    closeModal(force = false) {
        // 记录当前状态
        console.log('关闭模态框 - 当前状态:', {
            isEditing: this.isEditing,
            currentNoteId: this.currentNoteId,
            justSaved: this.justSaved,
            hasChanges: !this.justSaved && this.hasUnsavedChanges(),
            force: force
        });

        // 如果是强制关闭、刚保存过或没有未保存的更改，直接关闭
        if (force || this.justSaved || !this.hasUnsavedChanges()) {
            document.getElementById('noteModal').classList.add('hidden');
            this.resetForm();
            this.justSaved = false; // 重置保存标志
            return;
        }

        // 有未保存的更改，显示确认框
        if (confirm('有未保存的更改，确定要关闭吗？')) {
            document.getElementById('noteModal').classList.add('hidden');
            this.resetForm();
            this.justSaved = false; // 重置保存标志
        }
    }
    
    resetForm() {
        console.log('重置表单前:', { isEditing: this.isEditing, currentNoteId: this.currentNoteId });
        document.getElementById('noteForm').reset();
        document.getElementById('noteId').value = this.currentNoteId || '';
        this.selectedTags = [];
        this.renderSelectedTags();
        this.clearImagePreview();

        // 重置图片相关数据
        this.existingImages = [];
        this.imagesToDelete = [];
        if (window.notesManager) {
            window.notesManager.uploadedImages = [];
        }

        // 不再重置编辑状态和ID
        // this.currentNoteId = null;
        // this.isEditing = false;

        this.originalData = null; // 清空原始数据
        this.justSaved = false; // 重置保存标志
        console.log('重置表单后:', { isEditing: this.isEditing, currentNoteId: this.currentNoteId });
    }
    
    fillForm(noteData) {
        console.log('fillForm - 接收的数据:', noteData);

        document.getElementById('noteId').value = noteData.id || '';
        document.getElementById('noteTitle').value = noteData.title || '';
        document.getElementById('noteContent').value = noteData.content || '';
        document.getElementById('noteCategory').value = noteData.category_id || '';
        document.getElementById('notePublic').checked = noteData.is_public !== false;
        document.getElementById('notePinned').checked = noteData.is_pinned === true;

        // 设置选中的标签
        this.selectedTags = noteData.tags || [];
        this.renderSelectedTags();

        // 存储现有图片
        this.existingImages = noteData.images || [];
        this.imagesToDelete = [];

        // 显示现有图片
        this.renderAllImages();

        // 保存原始数据用于比较，确保深拷贝
        this.originalData = {
            id: noteData.id,
            title: noteData.title || '',
            content: noteData.content || '',
            category_id: noteData.category_id || '',
            is_public: noteData.is_public !== false,
            is_pinned: noteData.is_pinned === true,
            tag_ids: (noteData.tags || []).map(tag => tag.id)
        };

        console.log('fillForm - 原始数据设置为:', this.originalData);
    }
    
    renderSelectedTags() {
        const container = document.getElementById('tagsContainer');
        
        if (this.selectedTags.length === 0) {
            container.innerHTML = '<div class="text-gray-500 dark:text-gray-400 text-sm py-2">点击选择标签</div>';
            return;
        }
        
        const html = this.selectedTags.map(tag => `
            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white" style="background-color: ${tag.color}">
                ${tag.name}
                <button type="button" class="ml-1 text-white hover:text-gray-200" onclick="notesEditor.removeTag(${tag.id})">
                    <i class="fas fa-times text-xs"></i>
                </button>
            </span>
        `).join('');
        
        container.innerHTML = html + '<div class="text-gray-500 dark:text-gray-400 text-sm py-1 cursor-pointer" onclick="notesEditor.showTagSelector()">+ 添加标签</div>';
    }

    renderAllImages() {
        const container = document.querySelector('#imagePreview');
        if (!container) return;

        // 获取新上传的图片
        const newImages = window.notesManager ? window.notesManager.uploadedImages || [] : [];

        // 过滤掉要删除的现有图片
        const validExistingImages = this.existingImages.filter(img => !this.imagesToDelete.includes(img));

        const allImages = [];

        // 添加现有图片
        validExistingImages.forEach(imagePath => {
            allImages.push({
                type: 'existing',
                src: imagePath,
                name: imagePath.split('/').pop(),
                path: imagePath
            });
        });

        // 添加新上传的图片
        newImages.forEach((img, index) => {
            allImages.push({
                type: 'new',
                src: img.dataUrl,
                name: img.name,
                index: index
            });
        });

        if (allImages.length > 0) {
            const html = allImages.map(img => `
                <div class="image-preview-item">
                    <img src="${img.src}" alt="${img.name}"
                         class="cursor-pointer hover:opacity-90 transition-opacity"
                         onclick="openImageFullscreen('${img.src}')"
                         title="点击预览图片">
                    <button type="button" onclick="notesEditor.removeImage('${img.type}', '${img.type === 'existing' ? img.path : img.index}')" class="image-preview-remove">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="image-preview-name">${img.name}</div>
                </div>
            `).join('');

            container.innerHTML = html;
            container.classList.remove('hidden');
        } else {
            container.innerHTML = '';
            container.classList.add('hidden');
        }
    }

    clearImagePreview() {
        const container = document.querySelector('#imagePreview');
        if (container) {
            container.innerHTML = '';
            container.classList.add('hidden');
        }
    }

    removeImage(type, identifier) {
        if (type === 'existing') {
            // 标记现有图片为删除
            if (!this.imagesToDelete.includes(identifier)) {
                this.imagesToDelete.push(identifier);
            }
        } else if (type === 'new') {
            // 删除新上传的图片
            if (window.notesManager && window.notesManager.uploadedImages) {
                const index = parseInt(identifier);
                window.notesManager.uploadedImages.splice(index, 1);
            }
        }

        // 重新渲染图片
        this.renderAllImages();
    }
    
    showTagSelector() {
        // 创建标签选择器弹窗
        const availableTags = this.tags.filter(tag => 
            !this.selectedTags.some(selected => selected.id === tag.id)
        );
        
        if (availableTags.length === 0) {
            if (window.notesManager) {
                window.notesManager.showToast('所有标签都已选择', 'info');
            }
            return;
        }
        
        const tagOptions = availableTags.map(tag => 
            `<label class="flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded cursor-pointer">
                <input type="checkbox" value="${tag.id}" class="tag-checkbox">
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white" style="background-color: ${tag.color}">
                    ${tag.name}
                </span>
            </label>`
        ).join('');
        
        const popup = document.createElement('div');
        popup.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        popup.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">选择标签</h3>
                </div>
                <div class="p-4 max-h-64 overflow-y-auto">
                    ${tagOptions}
                </div>
                <div class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
                    <button type="button" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded hover:bg-gray-300 dark:hover:bg-gray-500" onclick="this.closest('.fixed').remove()">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onclick="notesEditor.addSelectedTags(this)">
                        确定
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(popup);
    }
    
    addSelectedTags(button) {
        const popup = button.closest('.fixed');
        const checkedBoxes = popup.querySelectorAll('.tag-checkbox:checked');
        
        checkedBoxes.forEach(checkbox => {
            const tagId = parseInt(checkbox.value);
            const tag = this.tags.find(t => t.id === tagId);
            if (tag && !this.selectedTags.some(selected => selected.id === tagId)) {
                this.selectedTags.push(tag);
            }
        });
        
        this.renderSelectedTags();
        popup.remove();
    }
    
    removeTag(tagId) {
        this.selectedTags = this.selectedTags.filter(tag => tag.id !== tagId);
        this.renderSelectedTags();
    }
    
    async saveNote() {
        try {
            const formData = this.getFormData();
            console.log('保存笔记 - 表单数据:', formData);
            console.log('保存笔记 - 状态:', { 
                isEditing: this.isEditing, 
                currentNoteId: this.currentNoteId 
            });

            // 验证表单
            if (!this.validateForm(formData)) {
                return;
            }
            
            // 检查置顶权限 - 只有管理员可以置顶
            if (window.notesManager && window.notesManager.userRole !== 'admin' && formData.is_pinned) {
                this.showToast('普通用户无法置顶笔记，请取消置顶选项', 'error');
                return;
            }

            // 显示保存状态
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '保存中...';
            saveBtn.disabled = true;

            const token = localStorage.getItem('token');
            
            // 确保编辑模式下URL包含笔记ID
            let url, method;
            if (this.isEditing && this.currentNoteId) {
                url = `/api/notes/${this.currentNoteId}`;
                method = 'PUT';
                console.log('编辑现有笔记:', this.currentNoteId);
            } else {
                url = '/api/notes';
                method = 'POST';
                console.log('创建新笔记');
            }
            
            console.log('保存请求:', { url, method });

            // 创建FormData对象以支持文件上传
            const submitData = new FormData();

            // 添加基本字段
            submitData.append('title', formData.title);
            submitData.append('content', formData.content);
            if (formData.category_id) {
                submitData.append('category_id', formData.category_id);
            }
            submitData.append('is_public', formData.is_public);
            submitData.append('is_pinned', formData.is_pinned);

            // 添加标签ID（如果有）
            if (formData.tag_ids && formData.tag_ids.length > 0) {
                formData.tag_ids.forEach(tagId => {
                    submitData.append('tag_ids[]', tagId);
                });
            }

            // 添加上传的图片
            if (window.notesManager && window.notesManager.uploadedImages) {
                window.notesManager.uploadedImages.forEach(imageData => {
                    submitData.append('images', imageData.file);
                });
            }

            // 添加要删除的图片列表
            if (this.imagesToDelete.length > 0) {
                this.imagesToDelete.forEach(imagePath => {
                    submitData.append('delete_images[]', imagePath);
                });
            }

            // 创建请求配置
            const requestConfig = {
                method: method,
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            };

            // 统一使用FormData，服务器端已支持PUT请求的文件上传
            requestConfig.body = submitData;

            const response = await fetch(url, requestConfig);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '保存失败');
            }

            const data = await response.json();

            // 清空上传的图片
            if (window.notesManager) {
                window.notesManager.uploadedImages = [];
                window.notesManager.renderImagePreview();
            }

            // 保存成功后更新原始数据，避免关闭时提示未保存
            const currentFormData = this.getFormData();
            this.originalData = {
                title: currentFormData.title,
                content: currentFormData.content,
                category_id: currentFormData.category_id || '',
                is_public: currentFormData.is_public,
                is_pinned: currentFormData.is_pinned,
                tag_ids: currentFormData.tag_ids
            };
            
            // 设置保存成功标志
            this.justSaved = true;

            // 显示成功消息
            if (window.notesManager) {
                window.notesManager.showToast(data.message, 'success');
                // 刷新笔记列表
                await window.notesManager.refresh();
            }

            // 关闭模态框
            this.closeModal();

        } catch (error) {
            console.error('保存笔记失败:', error);
            if (window.notesManager) {
                window.notesManager.showToast(error.message || '保存失败', 'error');
            }
        } finally {
            // 恢复按钮状态
            const saveBtn = document.getElementById('saveBtn');
            saveBtn.textContent = '保存';
            saveBtn.disabled = false;
        }
    }
    
    getFormData() {
        return {
            title: document.getElementById('noteTitle').value.trim(),
            content: document.getElementById('noteContent').value.trim(),
            category_id: document.getElementById('noteCategory').value || null,
            tag_ids: this.selectedTags.map(tag => tag.id),
            is_public: document.getElementById('notePublic').checked,
            is_pinned: document.getElementById('notePinned').checked
        };
    }
    
    validateForm(formData) {
        if (!formData.title) {
            if (window.notesManager) {
                window.notesManager.showToast('请输入笔记标题', 'error');
            }
            document.getElementById('noteTitle').focus();
            return false;
        }

        if (formData.title.length > 255) {
            if (window.notesManager) {
                window.notesManager.showToast('标题长度不能超过255个字符', 'error');
            }
            document.getElementById('noteTitle').focus();
            return false;
        }

        return true;
    }
    
    hasUnsavedChanges() {
        // 如果是新建模式，只要有内容就认为有更改
        if (!this.isEditing) {
            const title = document.getElementById('noteTitle').value.trim();
            const content = document.getElementById('noteContent').value.trim();
            return title || content;
        }

        // 如果是编辑模式，比较当前值与原始值
        if (!this.originalData) {
            return false; // 没有原始数据，说明还没有加载完成，不算有更改
        }

        const currentData = {
            title: document.getElementById('noteTitle').value.trim(),
            content: document.getElementById('noteContent').value.trim(),
            category_id: document.getElementById('noteCategory').value || '',
            is_public: document.getElementById('notePublic').checked,
            is_pinned: document.getElementById('notePinned').checked,
            tag_ids: this.selectedTags.map(tag => tag.id)
        };

        // 转换为JSON字符串进行深度比较
        const originalDataStr = JSON.stringify({
            title: this.originalData.title || '',
            content: this.originalData.content || '',
            category_id: this.originalData.category_id || '',
            is_public: this.originalData.is_public,
            is_pinned: this.originalData.is_pinned,
            tag_ids: (this.originalData.tag_ids || []).sort()
        });

        const currentDataStr = JSON.stringify({
            title: currentData.title || '',
            content: currentData.content || '',
            category_id: currentData.category_id || '',
            is_public: currentData.is_public,
            is_pinned: currentData.is_pinned,
            tag_ids: (currentData.tag_ids || []).sort()
        });

        console.log('比较数据:', {
            原始数据: JSON.parse(originalDataStr),
            当前数据: JSON.parse(currentDataStr),
            有更改: originalDataStr !== currentDataStr
        });

        return originalDataStr !== currentDataStr;
    }
    
    async editNote(noteId) {
        try {
            console.log('开始编辑笔记:', noteId);
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/notes/${noteId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('获取笔记详情失败');
            }
            
            const data = await response.json();
            console.log('编辑笔记 - 获取的数据:', data.data);

            // 明确设置编辑状态和笔记ID
            this.isEditing = true;
            this.currentNoteId = parseInt(noteId);
            console.log('设置编辑状态:', { isEditing: this.isEditing, currentNoteId: this.currentNoteId });

            this.openModal(data.data);
            
        } catch (error) {
            console.error('加载笔记详情失败:', error);
            if (window.notesManager) {
                window.notesManager.showToast('加载笔记详情失败', 'error');
            }
        }
    }
}

// 确保类在全局可用（立即执行）
window.NotesEditor = NotesEditor;

// 全局实例
let notesEditor;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 防止重复初始化
    if (!window.notesEditor) {
        try {
            notesEditor = new NotesEditor();
            window.notesEditor = notesEditor; // 供其他模块使用
        } catch (error) {
            console.error('笔记编辑器初始化失败:', error);
        }
    }
});
